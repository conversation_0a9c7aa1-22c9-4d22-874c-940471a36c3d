<script lang="ts">
  import { enhance } from '$app/forms';
  import { onMount } from 'svelte';
  import type { PageServerData, ActionData } from './$types';
  
  export let data: PageServerData;
  export let form: ActionData;
  
  let processing: string[] = [];
  
  onMount(async () => {
    // Commented out animations
    /*
    const anime = ((await import('animejs')) as any).default || (await import('animejs'));
    
    const tl = anime.timeline({
      easing: 'easeOutCubic'
    });

    tl
      .add({
        targets: '.requests-container',
        opacity: [0, 1],
        translateY: [30, 0],
        duration: 800
      })
      .add({
        targets: '.request-item',
        opacity: [0, 1],
        translateY: [20, 0],
        duration: 600,
        delay: anime.stagger(100)
      });
    */
  });
</script>

<div class="max-w-4xl mx-auto">
  <div class="requests-container" style="opacity: 0">
    <h1 class="text-3xl font-bold text-primary mb-8 text-center">Join Requests</h1>
    
    {#if data && data.requests.length === 0}
      <div class="card-glass text-center py-12">
        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <p class="text-xl text-gray-600">No pending access requests</p>
        <a href="/d/" class="btn-neumorphic mt-6 inline-block">
          Back to Dashboard
        </a>
      </div>
    {:else if data}
      <div class="space-y-4">
        {#each data.requests as request}
          <div 
            id="request-{request.id}"
            class="request-item card-glass flex items-center justify-between"
          >
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-800">{request.n}</h3>
              <div class="flex gap-4 mt-1 text-sm text-gray-600">
                <span>Role: <strong class="capitalize">{request.role}</strong></span>
                <span>School: <strong>{request.school?.n || 'Unknown'}</strong></span>
              </div>
            </div>
            
            <div class="flex gap-2">
              <form method="POST" action="?/grant" use:enhance>
                <input type="hidden" name="requestId" value={request.id} />
                <button
                  type="submit"
                  class="p-3 rounded-lg bg-green-100 hover:bg-green-200 transition-colors disabled:opacity-50"
                  title="Grant Access"
                >
                  <svg class="w-5 h-5 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </button>
              </form>
              
              <form method="POST" action="?/reject" use:enhance>
                <input type="hidden" name="requestId" value={request.id} />
                <button
                  type="submit"
                  class="p-3 rounded-lg bg-red-100 hover:bg-red-200 transition-colors disabled:opacity-50"
                  title="Reject Access"
                >
                  <svg class="w-5 h-5 text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </form>
            </div>
          </div>
        {/each}
      </div>
      
      <div class="mt-8 text-center">
        <a href="/d/" class="btn-neumorphic">
          Back to Dashboard
        </a>
      </div>
    {/if}
  </div>
</div>