import { fail } from '@sveltejs/kit';
import {
	upsertPoint,
	deleteById,
	searchByPayload
} from '$lib/db';
import type { Actions, PageServerLoad } from './$types';
import type { AccessRequest, SchoolUser } from '$lib/types';
import { requireAuth } from '$lib/auth';

export const actions: Actions = {
	grant: async (event) => {
		const session = await requireAuth(event.locals);
		const { request } = event;

		const data = await request.formData();
		const requestId = data.get('requestId')?.toString();

		if (!requestId) {
			return fail(400, { error: 'Request ID is required' });
		}

		try {
			// Get the request to validate
			const requests = await searchByPayload<AccessRequest>({
				id: requestId,
				s: 'sch_aro'
			});

			const accessRequest = requests[0];
			if (!accessRequest) {
				return fail(404, { error: 'Access request not found' });
			}

			// Create school user
			const schoolUser: SchoolUser = {
				s: 'sch_usr',
				u: accessRequest.u,
				n: accessRequest.n,
				r: accessRequest.role,
				sc: accessRequest.sc
			};

			await upsertPoint(schoolUser);

			// Delete the request
			await deleteById(requestId);

			return { success: true };
		} catch (e) {
			console.error('Error granting access:', e);
			return fail(500, { error: 'Failed to grant access' });
		}
	},

	reject: async (event) => {
		const session = await requireAuth(event.locals);
		const { request } = event;

		const data = await request.formData();
		const requestId = data.get('requestId')?.toString();

		if (!requestId) {
			return fail(400, { error: 'Request ID is required' });
		}

		try {
			// Get the request to validate
			const requests = await searchByPayload<AccessRequest>({
				id: requestId,
				s: 'sch_aro'
			});

			const accessRequest = requests[0];
			if (!accessRequest) {
				return fail(404, { error: 'Access request not found' });
			}

			// Update request as rejected
			const updatedRequest: AccessRequest = {
				...accessRequest,
				r: true
			};

			await upsertPoint(updatedRequest);

			return { success: true };
		} catch (e) {
			console.error('Error rejecting access:', e);
			return fail(500, { error: 'Failed to reject access request' });
		}
	}
};
