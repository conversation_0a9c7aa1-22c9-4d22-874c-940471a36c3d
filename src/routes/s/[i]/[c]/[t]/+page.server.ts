import { getById } from '$lib/db';
import { searchByPayload } from '$lib/db';
import type { PageServerLoad } from './$types';
import type { School, SchoolUser, Score } from '$lib/types';
import { error, redirect } from '@sveltejs/kit';
import { subjects } from "$lib/constants";
import { requireAuth } from '$lib/auth';
export const load: PageServerLoad = async ({ params, locals }) => {
  const session = await requireAuth(locals);
  const { i, c, t } = params;
  const school_user: SchoolUser | null = await getById(i)
  if (!school_user) throw error(404, 'user not found')
  const school: School | null = await getById(school_user.sc)
  if (!school) throw error(404, 'school not found')

  let admin = true;

  // find admin profiles
  // const admin_res = await searchByPayload({
  //   s: 'sch_usr',
  //   sc: school.id,
  //   u: session.user?.id,
  //   r: 'admin'
  // })

  // if (admin_res.length > 0) { admin = true }

  const _class = parseInt(c);
  const termNum = parseInt(t);
  
  // Fetch all scores for this student, c, and t
  const scores: Score[] = await searchByPayload({
    s: 'sch_scr',
    u: i,
    c: _class,
    y: 2024,
    t: termNum
  });

  console.log('scores', scores);

  return {
    scores,
    subjects,
    admin,
    i,
    c,
    t,
    studentName: school_user.n
  };
}; 