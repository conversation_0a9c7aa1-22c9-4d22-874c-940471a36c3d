import { error, fail, redirect } from '@sveltejs/kit';
import { getById, searchByPayload, upsertPoint } from '$lib/db';
import type { Actions, PageServerLoad } from './$types';
import type { School, SchoolUser, Score } from '$lib/types';
import { requireAuth } from '$lib/auth';

export const load: PageServerLoad = async ({ params, locals }) => {
  const session = await requireAuth(locals);
  // params: id, year, t
  const { i, c, t } = params;
  // Provide all subject codes and names for the form
  const school_user: SchoolUser | null = await getById(i)
  if (!school_user) throw error(401, 'user not found')
  const school: School | null = await getById(school_user.sc)
  if (!school) throw error(401, 'school not found')

  let scores: Score[] = [];
  try {
    scores = await searchByPayload({
      s: 'sch_scr',
      u: i,
      c: parseInt(c),
      y: 2024,
      t: parseInt(t)
    });
  } catch (error) {
    console.error('Error fetching scores:', error);
    // Continue with empty scores array
  }

  return {
    id: i,
    year: c,
    term: t,
    scores,
  };
};

export const actions: Actions = {
  default: async ({ request, params }) => {
    const form = await request.formData();
    const { i, c, t } = params;
    // id: uuid (string), c: number (class key), t: number (t number)
    if (!i || !c || !t) {
      return fail(400, { error: 'Missing student, class, or t' });
    }
    const classKey = parseInt(c);
    const termNum = parseInt(t);
    try {
      // Group form entries by subject code
      const subjectScores: { [subjectCode: string]: { ca1?: number; ca2?: number } } = {};

      for (const [key, value] of form.entries()) {
        if (key.startsWith('ca1_')) {
          const subjectCode = key.slice('ca1_'.length);
          if (value !== null && value !== '') {
            if (!subjectScores[subjectCode]) subjectScores[subjectCode] = {};
            subjectScores[subjectCode].ca1 = parseInt(value.toString());
          }
        } else if (key.startsWith('ca2_')) {
          const subjectCode = key.slice('ca2_'.length);
          if (value !== null && value !== '') {
            if (!subjectScores[subjectCode]) subjectScores[subjectCode] = {};
            subjectScores[subjectCode].ca2 = parseInt(value.toString());
          }
        }
      }

      // Create or update scores for each subject
      for (const [subjectCode, scores] of Object.entries(subjectScores)) {
        const score: Score = {
          s: 'sch_scr',
          u: i,
          t: termNum as 1 | 2 | 3,
          c: classKey,
          y: 2024,
          j: subjectCode,
          ...(scores.ca1 !== undefined && { 1: scores.ca1 }),
          ...(scores.ca2 !== undefined && { 2: scores.ca2 })
        };
        await upsertPoint(score);
      }
      // Redirect back to student page or success page
      redirect(303, `/s/${i}/${c}/${t}`);
    } catch (error) {
      console.error('Error saving scores:', error);
      return fail(500, { error: 'Failed to save scores' });
    }
  }
};
